// Variables globales
let dashboardData = {};
let revenueChart = null;
let currentSection = 'dashboard';

// La fonction showSection est maintenant gérée par dashboard-navigation.js

// Vérifier l'authentification au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    if (!isAuthenticated()) {
        window.location.href = '/./auth.html?return=' + encodeURIComponent(window.location.pathname);
        return;
    }
    
    // Vérifier le rôle opérateur
    const user = getCurrentUser();
    if (!user || !user.roles || !user.roles.includes('operator')) {
        showAlert('Accès non autorisé. Vous devez être opérateur.', 'danger');
        setTimeout(() => {
            window.location.href = 'dashboard.html';
        }, 2000);
        return;
    }
    
    initializeOperatorDashboard();
});

// Initialiser le tableau de bord opérateur
async function initializeOperatorDashboard() {
    try {
        // Afficher les informations utilisateur
        displayUserInfo();

        // Charger les données de base pour les formulaires
        await loadBaseData();

        // Gérer la validation des tickets
        setupTicketValidation();

        // Charger la page dashboard par défaut
        if (typeof showSection === 'function') {
            showSection('dashboard');
        }

    } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        showAlert('Erreur lors du chargement du tableau de bord', 'danger');
    }
}

// Charger les données de base nécessaires pour les formulaires
async function loadBaseData() {
    try {
        // Charger les lieux pour les selects
        const locationsResponse = await apiRequest('operator/locations');
        if (locationsResponse && locationsResponse.locations) {
            populateLocationSelects(locationsResponse.locations);
        }
    } catch (error) {
        console.error('Erreur lors du chargement des données de base:', error);
    }
}

// Peupler les selects de lieux
function populateLocationSelects(locations) {
    // Tous les selects qui utilisent les lieux
    const selects = ['stopLocationId', 'routeDepartureFilter', 'stopLocationFilter'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Garder la première option
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            locations.forEach(location => {
                if (location.status === 'active') {
                    const option = document.createElement('option');
                    option.value = location.location_id;
                    option.textContent = location.location_name;
                    select.appendChild(option);
                }
            });
        }
    });

    // Peupler le filtre pays
    const countryFilter = document.getElementById('locationCountryFilter');
    if (countryFilter && locations && locations.length > 0) {
        const countries = [...new Set(locations.map(l => l.country))];
        const firstOption = countryFilter.querySelector('option');
        countryFilter.innerHTML = '';
        if (firstOption) {
            countryFilter.appendChild(firstOption);
        }

        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            countryFilter.appendChild(option);
        });
    }
}

// Afficher les informations utilisateur
function displayUserInfo() {
    const user = getCurrentUser();
    if (user) {
        document.getElementById('operatorName').textContent = user.first_name || 'Opérateur';
    }
}

// Charger les données du tableau de bord
async function loadDashboardData() {
    try {
        // Appel API pour récupérer les données du tableau de bord opérateur
        const response = await apiRequest('operator/dashboard');
        
        dashboardData = response;
        
        if (response.stats) {
            displayStats(response.stats);
        }
        
        if (response.today_trips) {
            displayTodayTrips(response.today_trips);
        }
        
        if (response.alerts) {
            displayAlerts(response.alerts);
        }
        
        // Charger le graphique des revenus
        await loadRevenueChart();
        
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Afficher les statistiques
function displayStats(stats) {
    document.getElementById('totalBookingsToday').textContent = stats.bookings?.today.total || 0;
    document.getElementById('activeTrips').textContent = stats.trips?.ongoing || 0;
    document.getElementById('revenueToday').textContent = formatCurrency(stats.revenue?.today || 0);
    
    // Calculer le nombre de passagers (approximation basée sur les réservations)
    const avgPassengersPerBooking = 1.5; // Moyenne estimée
    const totalPassengers = Math.round((stats.bookings?.today.total || 0) * avgPassengersPerBooking);
    document.getElementById('totalPassengers').textContent = totalPassengers;
}

// Afficher les voyages du jour
function displayTodayTrips(trips) {
    const container = document.getElementById('todayTripsContainer');
    
    if (!trips || trips.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun voyage prévu aujourd'hui</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += `
        <thead>
            <tr>
                <th>Trajet</th>
                <th>Heure départ</th>
                <th>Bus</th>
                <th>Réservations</th>
                <th>Statut</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
    `;
    
    trips.forEach(trip => {
        const occupancyRate = trip.total_seats > 0 ? Math.round((trip.booked_seats / trip.total_seats) * 100) : 0;
        const statusClass = trip.status === 'ongoing' ? 'success' : trip.status === 'completed' ? 'secondary' : 'primary';
        
        html += `
            <tr>
                <td>
                    <strong>${trip.route_name}</strong><br>
                    <small class="text-muted">${trip.departure_location} → ${trip.destination_location}</small>
                </td>
                <td>${formatTime(trip.estimated_departure_time)}</td>
                <td>${trip.bus_registration || 'N/A'}</td>
                <td>
                    ${trip.booked_seats}/${trip.total_seats}
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar" style="width: ${occupancyRate}%"></div>
                    </div>
                </td>
                <td><span class="badge bg-${statusClass}">${getStatusText(trip.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewTripDetails(${trip.trip_id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher les alertes
function displayAlerts(alerts) {
    const container = document.getElementById('alertsContainer');
    
    if (!alerts || alerts.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                <p class="text-muted">Aucune alerte</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    alerts.forEach(alert => {
        const iconClass = alert.type === 'danger' ? 'exclamation-triangle' : 
                         alert.type === 'warning' ? 'exclamation-circle' : 'info-circle';
        
        html += `
            <div class="alert-item border-start border-${alert.type} border-3">
                <div class="d-flex align-items-start">
                    <i class="fas fa-${iconClass} text-${alert.type} me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${alert.message}</div>
                        ${alert.action ? `<small class="text-muted">${alert.action}</small>` : ''}
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Charger le graphique des revenus
async function loadRevenueChart() {
    const chartCanvasId = 'revenueChart';
    const chartContainerEl = document.getElementById(chartCanvasId)?.parentElement;

    if (!chartContainerEl) {
        console.error(`Le conteneur du graphique pour '${chartCanvasId}' n'a pas été trouvé.`);
        return;
    }

    // Fonction pour réinitialiser la zone du canvas et afficher un message
    const resetCanvasAreaWithMessage = (messageHtml) => {
        if (revenueChart) {
            revenueChart.destroy();
            revenueChart = null;
        }
        // S'assure que le titre est toujours présent et ajoute le message et un canvas caché
        chartContainerEl.innerHTML = `
            <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>Évolution des revenus (7 derniers jours)</h5>
            ${messageHtml}
            <canvas id="${chartCanvasId}" height="100" style="display: none;"></canvas> 
        `;
    };

    const ensureCanvasIsReady = () => {
        let canvas = document.getElementById(chartCanvasId);
        if (!canvas) { // Si le canvas n'existe pas (a été retiré par un message d'erreur)
            chartContainerEl.innerHTML = `
                <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>Évolution des revenus (7 derniers jours)</h5>
                <canvas id="${chartCanvasId}" height="100"></canvas>
            `;
            canvas = document.getElementById(chartCanvasId);
        } else {
            canvas.style.display = 'block'; // S'assurer qu'il est visible s'il était caché
        }
        return canvas;
    };


    try {
        if (typeof Chart === 'undefined') {
            resetCanvasAreaWithMessage(`
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                    <p class="text-muted">Erreur : La bibliothèque de graphiques (Chart.js) n'a pas pu être chargée.</p>
                    <p class="text-muted">Veuillez vérifier votre connexion Internet ou les scripts de la page.</p>
                </div>`);
            return;
        }

        const response = await apiRequest('operator/analytics');
        const rawRevenueData = response.analytics?.revenue_trends || [];

        // Nettoyer et traiter les données: s'assurer que 'revenue' est un nombre valide
        const processedRevenueData = rawRevenueData
            .map(item => ({
                date: item.date,
                revenue: (typeof item.revenue === 'number' && isFinite(item.revenue)) ? item.revenue : null
            }))
            .filter(item => item.revenue !== null); // Garder seulement les entrées avec des revenus valides

        const labels = processedRevenueData.map(item => formatDate(item.date));
        const revenues = processedRevenueData.map(item => item.revenue);

        if (revenues.length === 0) {
            resetCanvasAreaWithMessage(`
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Aucune donnée de revenus disponible pour afficher le graphique.</p>
                </div>`);
            return;
        }
        
        const canvasElement = ensureCanvasIsReady();
        if (!canvasElement) {
            console.error(`L'élément Canvas '${chartCanvasId}' n'a pas pu être préparé.`);
            return;
        }
        const ctx = canvasElement.getContext('2d');

        if (revenueChart) {
            revenueChart.destroy();
            revenueChart = null;
        }
        
        revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenus (FCFA)',
                    data: revenues,
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += formatCurrency(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        },
                        // Suggère un max pour l'axe Y pour une meilleure lisibilité si les données sont très faibles.
                        // Si le revenu max est < 500, on met l'axe à 1000, sinon Chart.js décide.
                        suggestedMax: Math.max(...revenues, 0) < 500 ? 1000 : undefined
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 0,
                            autoSkip: true,
                            maxTicksLimit: labels.length > 7 ? 7 : labels.length, 
                        }
                    }
                }
            }
        });
        
    } catch (error) {
        console.error('Erreur lors du chargement du graphique:', error);
        resetCanvasAreaWithMessage(`
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                <p class="text-muted">Erreur lors du chargement du graphique : ${error.message || 'Erreur inconnue'}</p>
            </div>`);
    }
}






// Charger les données spécifiques à chaque section
async function loadSectionData(sectionName) {
    switch (sectionName) {
        case 'dashboard':
            await loadDashboard();
            break;
        case 'locations':
            await loadLocations();
            break;
        case 'stops':
            await loadStops();
            break;
        case 'amenities':
            await loadAmenities();
            break;
        case 'seatPlans':
            await loadSeatPlans();
            break;
        case 'routes':
            await loadRoutes();
            break;
        case 'pricing':
            await loadPricing();
            break;
        case 'payments':
            await loadPayments();
            break;
        case 'users':
            await loadUsers();
            break;
        case 'bookings':
            await loadBookings();
            break;
        case 'trips':
            await loadTrips();
            break;
        case 'buses':
            await loadBuses();
            break;
        case 'reports':
            // Les rapports sont générés à la demande
            break;
    }
}

// Charger les données du dashboard
async function loadDashboard() {
    await loadDashboardData();
}

// Configuration de la validation des tickets
function setupTicketValidation() {
    // Gérer la touche Entrée dans le champ de saisie
    document.getElementById('ticketCodeInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            validateTicket();
        }
    });
}

// Valider un ticket
async function validateTicket() {
    const ticketCode = document.getElementById('ticketCodeInput').value.trim();
    const resultContainer = document.getElementById('validationResult');
    
    if (!ticketCode) {
        showAlert('Veuillez saisir un code de ticket', 'warning');
        return;
    }
    
    try {
        // Afficher l'état de chargement
        resultContainer.innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                <p>Validation en cours...</p>
            </div>
        `;
        
        // Appel API pour valider le ticket
        const response = await apiRequest('tickets/validate', {
            method: 'POST',
            body: JSON.stringify({
                ticket_code: ticketCode
            })
        });
        
        // Afficher le résultat de la validation
        resultContainer.innerHTML = `
            <div class="alert alert-success">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">Ticket validé avec succès !</h5>
                        <p class="mb-1"><strong>Passager :</strong> ${response.passenger_name}</p>
                        <p class="mb-0"><strong>Siège :</strong> ${response.seat_number}</p>
                    </div>
                </div>
            </div>
        `;
        
        // Vider le champ de saisie
        document.getElementById('ticketCodeInput').value = '';
        document.getElementById('ticketCodeInput').focus();
        
        showAlert('Ticket validé avec succès', 'success');
        
    } catch (error) {
        console.error('Erreur lors de la validation:', error);
        
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <div class="d-flex align-items-center">
                    <i class="fas fa-times-circle fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">Validation échouée</h5>
                        <p class="mb-0">${error.message}</p>
                    </div>
                </div>
            </div>
        `;
        
        showAlert(error.message, 'danger');
    }
}

// Scanner QR Code (fonctionnalité future)
function startQRScanner() {
    showAlert('Scanner QR Code - Fonctionnalité en cours de développement', 'info');
}

// Afficher l'historique de validation
function showValidationHistory() {
    showAlert('Historique de validation - Fonctionnalité en cours de développement', 'info');
}

// Voir les détails d'un voyage
function viewTripDetails(tripId) {
    showAlert(`Détails du voyage #${tripId} - Fonctionnalité en cours de développement`, 'info');
}

// Fonctions utilitaires
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0
    }).format(amount) + ' FCFA';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit'
    });
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getStatusText(status) {
    const statusMap = {
        'scheduled': 'Programmé',
        'ongoing': 'En cours',
        'completed': 'Terminé',
        'cancelled': 'Annulé'
    };
    return statusMap[status] || status;
}

// Fonction pour faire des requêtes API (réutilisée depuis auth.js)
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/${endpoint}`;
    const token = getAuthToken();
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        }
    };
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erreur de requête');
        }
        
        return data;
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

// Fonction pour afficher les alertes
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// ==================== GESTION DES RÉSERVATIONS ====================

// Charger les réservations
async function loadBookings() {
    try {
        const response = await apiRequest('operator/bookings');
        displayBookings(response.bookings);
    } catch (error) {
        console.error('Erreur lors du chargement des réservations:', error);
        showAlert('Erreur lors du chargement des réservations', 'danger');
    }
}

// Afficher les réservations
function displayBookings(bookings) {
    const container = document.getElementById('bookingsTableContainer');

    if (!bookings || bookings.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucune réservation trouvée</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Client</th>
                        <th>Trajet</th>
                        <th>Date voyage</th>
                        <th>Sièges</th>
                        <th>Montant</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    bookings.forEach(booking => {
        const statusClass = booking.status === 'confirmed' ? 'success' :
                           booking.status === 'pending' ? 'warning' : 'danger';

        html += `
            <tr>
                <td>#${booking.booking_id}</td>
                <td>
                    <div>
                        <strong>${booking.customer_name || 'N/A'}</strong><br>
                        <small class="text-muted">${booking.contact_email}</small>
                    </div>
                </td>
                <td>${booking.route_name}</td>
                <td>${formatDate(booking.trip_date)}</td>
                <td>${booking.seat_count} siège(s)</td>
                <td>${formatCurrency(booking.total_amount)}</td>
                <td><span class="badge bg-${statusClass}">${getStatusText(booking.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewBookingDetails(${booking.booking_id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editBooking(${booking.booking_id})">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Filtrer les réservations
async function filterBookings() {
    const status = document.getElementById('bookingStatusFilter').value;
    const date = document.getElementById('bookingDateFilter').value;
    const search = document.getElementById('bookingSearchFilter').value;

    try {
        const params = new URLSearchParams();
        if (status) params.append('status', status);
        if (date) params.append('date', date);
        if (search) params.append('search', search);

        const response = await apiRequest(`operator/bookings?${params.toString()}`);
        displayBookings(response.bookings);
    } catch (error) {
        console.error('Erreur lors du filtrage:', error);
        showAlert('Erreur lors du filtrage', 'danger');
    }
}

// Effacer les filtres de réservation
function clearBookingFilters() {
    document.getElementById('bookingStatusFilter').value = '';
    document.getElementById('bookingDateFilter').value = '';
    document.getElementById('bookingSearchFilter').value = '';
    loadBookings();
}

// Actualiser les réservations
function refreshBookings() {
    loadBookings();
    showAlert('Réservations actualisées', 'success');
}

// Exporter les réservations
function exportBookings() {
    showAlert('Export en cours de développement', 'info');
}

// ==================== GESTION DES VOYAGES ====================

// Charger les voyages
async function loadTrips() {
    try {
        const response = await apiRequest('operator/trips');
        displayTrips(response.trips);

        // Charger aussi les routes pour le filtre
        const routesResponse = await apiRequest('operator/routes');
        populateRouteFilter(routesResponse.routes);
    } catch (error) {
        console.error('Erreur lors du chargement des voyages:', error);
        showAlert('Erreur lors du chargement des voyages', 'danger');
    }
}

// Afficher les voyages
function displayTrips(trips) {
    const container = document.getElementById('tripsTableContainer');

    if (!trips || trips.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-route fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun voyage trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Trajet</th>
                        <th>Bus</th>
                        <th>Départ</th>
                        <th>Arrivée</th>
                        <th>Occupation</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    trips.forEach(trip => {
        const occupancyRate = trip.total_seats > 0 ?
            Math.round((trip.booked_seats / trip.total_seats) * 100) : 0;
        const statusClass = trip.status === 'ongoing' ? 'success' :
                           trip.status === 'completed' ? 'secondary' : 'primary';

        html += `
            <tr>
                <td>#${trip.trip_id}</td>
                <td>
                    <strong>${trip.route_name}</strong><br>
                    <small class="text-muted">${trip.departure_location} → ${trip.destination_location}</small>
                </td>
                <td>${trip.bus_registration || 'N/A'}</td>
                <td>${formatDateTime(trip.estimated_departure_time)}</td>
                <td>${formatDateTime(trip.estimated_arrival_time)}</td>
                <td>
                    ${trip.booked_seats}/${trip.total_seats} (${occupancyRate}%)
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar" style="width: ${occupancyRate}%"></div>
                    </div>
                </td>
                <td><span class="badge bg-${statusClass}">${getStatusText(trip.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewTripDetails(${trip.trip_id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editTrip(${trip.trip_id})">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Peupler le filtre des routes pour la section Voyages
function populateRouteFilter(routes) {
    // Tous les selects qui utilisent les routes pour les filtres
    const selects = ['tripRouteFilter', 'pricingRouteFilter'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Garder la première option (ex: "Toutes les routes")
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            if (routes && routes.length > 0) {
                routes.forEach(route => {
                    const option = document.createElement('option');
                    option.value = route.route_id;
                    option.textContent = route.route_name;
                    select.appendChild(option);
                });
            }
        } else {
            console.warn(`L'élément select avec l'ID '${selectId}' n'a pas été trouvé.`);
        }
    });
}

// Fonction de déconnexion
function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        removeAuthToken();
        localStorage.removeItem('userData');
        window.location.href = 'index.html';
    }
}
