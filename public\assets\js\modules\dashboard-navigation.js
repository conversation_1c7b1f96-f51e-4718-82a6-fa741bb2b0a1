/**
 * Module de navigation pour le tableau de bord opérateur
 * Gestion de la navigation entre sections avec chargement dynamique
 */

// Configuration des sections et leurs modales associées
const SECTION_CONFIG = {
    'dashboard': {
        title: 'Tableau de bord',
        modals: [],
        preload: true
    },
    'validation': {
        title: 'Validation des tickets',
        modals: [],
        preload: true
    },
    'locations': {
        title: 'Gestion des Lieux/Villes',
        modals: ['location'],
        preload: false
    },
    'stops': {
        title: 'Gestion des Arrêts',
        modals: ['stop'],
        preload: false
    },
    'amenities': {
        title: 'Gestion des Commodités',
        modals: ['amenity'],
        preload: false
    },
    'seatPlans': {
        title: 'Gestion des Plans de Sièges',
        modals: ['seat-plan'],
        preload: false
    },
    'buses': {
        title: 'Gestion des Bus',
        modals: ['bus'],
        preload: false
    },
    'seats': {
        title: 'Gestion des Sièges',
        modals: ['seat-management', 'seat'],
        preload: false
    },
    'routes': {
        title: 'Gestion des Itinéraires',
        modals: ['route'],
        preload: false
    },
    'trips': {
        title: 'Gestion des Voyages',
        modals: ['trip'],
        preload: false
    },
    'pricing': {
        title: 'Gestion de la Tarification',
        modals: ['pricing'],
        preload: false
    },
    'bookings': {
        title: 'Gestion des Réservations',
        modals: [],
        preload: false
    },
    'payments': {
        title: 'Suivi des Paiements',
        modals: [],
        preload: false
    },
    'users': {
        title: 'Gestion des Utilisateurs',
        modals: ['user'],
        preload: false
    },
    'reports': {
        title: 'Rapports et analyses',
        modals: [],
        preload: false
    }
};

// Variable pour suivre la section actuelle
let currentSection = 'dashboard';

/**
 * Fonction principale de navigation entre sections
 */
async function showSection(sectionName) {
    try {
        // Vérifier si la section existe
        if (!SECTION_CONFIG[sectionName]) {
            console.error(`Section inconnue: ${sectionName}`);
            showAlert('Section non trouvée', 'danger');
            return;
        }

        // Mettre à jour la navigation visuelle
        updateNavigation(sectionName);

        // Afficher un indicateur de chargement
        showLoadingIndicator();

        // Charger la page
        const content = await pageLoader.loadPage(sectionName);
        const mainContent = document.getElementById('main-content');
        if (mainContent) {
            mainContent.innerHTML = content;
        }

        // Charger les modales nécessaires
        await loadSectionModals(sectionName);

        // Initialiser les données de la section
        await initializeSectionData(sectionName);

        // Mettre à jour la section actuelle
        currentSection = sectionName;

        // Masquer l'indicateur de chargement
        hideLoadingIndicator();

    } catch (error) {
        console.error('Erreur lors du chargement de la section:', error);
        showAlert('Erreur lors du chargement de la page', 'danger');
        hideLoadingIndicator();
    }
}

/**
 * Met à jour la navigation visuelle
 */
function updateNavigation(sectionName) {
    // Retirer la classe active de tous les liens
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // Ajouter la classe active au lien correspondant
    const activeLink = document.querySelector(`.nav-link[onclick*="showSection('${sectionName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }

    // Mettre à jour le titre de la page
    const config = SECTION_CONFIG[sectionName];
    const titleElement = document.getElementById('pageTitle');
    if (titleElement && config) {
        titleElement.textContent = config.title;
    }
}

/**
 * Charge les modales nécessaires pour une section
 */
async function loadSectionModals(sectionName) {
    const config = SECTION_CONFIG[sectionName];
    if (config && config.modals && config.modals.length > 0) {
        try {
            await pageLoader.injectModals(config.modals);
        } catch (error) {
            console.warn(`Erreur lors du chargement des modales pour ${sectionName}:`, error);
        }
    }
}

/**
 * Initialise les données spécifiques à une section
 */
async function initializeSectionData(sectionName) {
    try {
        // Appeler la fonction de chargement spécifique si elle existe
        const loadFunctionName = `load${sectionName.charAt(0).toUpperCase() + sectionName.slice(1)}`;
        if (typeof window[loadFunctionName] === 'function') {
            await window[loadFunctionName]();
        }

        // Appeler loadSectionData si elle existe (fonction générique du dashboard)
        if (typeof window.loadSectionData === 'function') {
            await window.loadSectionData(sectionName);
        }
    } catch (error) {
        console.warn(`Erreur lors de l'initialisation des données pour ${sectionName}:`, error);
    }
}

/**
 * Affiche un indicateur de chargement
 */
function showLoadingIndicator() {
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
        mainContent.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-3 text-muted">Chargement en cours...</p>
            </div>
        `;
    }
}

/**
 * Masque l'indicateur de chargement
 */
function hideLoadingIndicator() {
    // L'indicateur est automatiquement masqué quand le contenu est remplacé
}

/**
 * Précharge les sections importantes
 */
async function preloadImportantSections() {
    const sectionsToPreload = Object.keys(SECTION_CONFIG)
        .filter(key => SECTION_CONFIG[key].preload);
    
    try {
        await pageLoader.preloadPages(sectionsToPreload);
    } catch (error) {
        console.warn('Erreur lors du préchargement des sections:', error);
    }
}

/**
 * Obtient la section actuelle
 */
function getCurrentSection() {
    return currentSection;
}

/**
 * Navigue vers une section avec gestion d'historique
 */
function navigateToSection(sectionName, updateHistory = true) {
    showSection(sectionName);
    
    if (updateHistory && window.history) {
        const url = new URL(window.location);
        url.searchParams.set('section', sectionName);
        window.history.pushState({ section: sectionName }, '', url);
    }
}

/**
 * Gère la navigation par l'historique du navigateur
 */
window.addEventListener('popstate', function(event) {
    if (event.state && event.state.section) {
        showSection(event.state.section);
    }
});

/**
 * Initialise la navigation au chargement de la page
 */
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier s'il y a une section dans l'URL
    const urlParams = new URLSearchParams(window.location.search);
    const sectionFromUrl = urlParams.get('section');
    
    if (sectionFromUrl && SECTION_CONFIG[sectionFromUrl]) {
        currentSection = sectionFromUrl;
    }

    // Précharger les sections importantes
    preloadImportantSections();
});

// Exposer les fonctions globalement pour maintenir la compatibilité
window.showSection = showSection;
window.getCurrentSection = getCurrentSection;
window.navigateToSection = navigateToSection;
