[2025-03-03 22:26:13] [ERROR] {"message":"<PERSON>ssource non trouv\u00e9e"}
[2025-03-03 22:44:36] [ERROR] {"message":"Token invalide"}
[2025-03-03 22:44:38] [ERROR] {"message":"Token invalide"}
[2025-03-03 22:48:34] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-03 22:48:52] [INFO] Utilisateur connecté avec succès
[2025-03-03 23:03:15] [ERROR] {"message":"Token invalide"}
[2025-03-03 23:06:31] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:29] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:30] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:32] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:37] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:48] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:28:22] [ERROR] {"message":"R\u00e9servation non trouv\u00e9e ou non autoris\u00e9e"}
[2025-03-04 17:32:11] [ERROR] {"message":"Token requis"}
[2025-03-04 17:32:31] [ERROR] {"message":"Token invalide"}
[2025-03-04 17:36:53] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-04 17:37:07] [INFO] Utilisateur connecté avec succès
[2025-03-04 17:39:37] [ERROR] {"message":"Token requis"}
[2025-03-04 17:39:39] [ERROR] {"message":"Token requis"}
[2025-03-04 17:39:56] [ERROR] {"message":"Token requis"}
[2025-03-07 11:41:28] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:41:47] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:41:52] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:44:36] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:44:49] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:44:53] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:45:36] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:45:39] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:45:43] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:46:47] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:46:54] [ERROR] {"message":"Token invalide"}
[2025-03-07 11:47:15] [ERROR] {"message":"Token invalide"}
[2025-03-07 13:26:55] [ERROR] {"error":"Vous n'\u00eates pas autoris\u00e9 \u00e0 vous connecter avec ce r\u00f4le"}
[2025-03-07 13:27:28] [ERROR] {"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjYsInJvbGUiOiJvcGVyYXRvciIsImlhdCI6MTc0MTM1NDA0OCwiZXhwIjoxNzQxNDQwNDQ4fQ.ig5LGBuHqasVEWlPoG9HK6Bz5LdQqfpl_UBtXH913iQ"}
[2025-03-07 13:46:25] [ERROR] {"message":"Le champ 'role' est requis"}
[2025-03-07 13:56:06] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 13:57:26] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9,  \u00e0 vous connecter avec ce r\u00f4le"}
[2025-03-07 13:57:26] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 13:58:52] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9,  \u00e0 vous connecter avec ce r\u00f4le"}
[2025-03-07 13:59:46] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9 ! Vous n'avez pas les droits suffisants pour acc\u00e9der \u00e0 cette ressource."}
[2025-03-07 14:00:08] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9 ! Vous n'avez pas les droits suffisants pour acc\u00e9der \u00e0 cette ressource."}
[2025-03-07 14:00:13] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9 ! Vous n'avez pas les droits suffisants pour acc\u00e9der \u00e0 cette ressource."}
[2025-03-07 14:03:10] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 14:04:42] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 15:30:25] [ERROR] {"message":"Token invalide"}
[2025-03-07 15:33:20] [ERROR] {"message":"Le champ 'route_id' est requis"}
[2025-03-07 17:22:35] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-07 17:22:49] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-07 17:24:40] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-07 20:56:30] [ERROR] {"message":"Version API invalide"}
[2025-03-08 11:50:48] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-08 19:51:15] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:54:48] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:54:57] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:55:04] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:56:56] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:57:18] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:57:37] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:12:38] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:12:42] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:12:47] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:32:16] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:32:16] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:32:54] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:33:19] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:45:02] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:06:29] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:12:42] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 21:14:30] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:14:58] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:15:14] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:15:14] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:01:06] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:01:25] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-08 23:21:16] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:23:24] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:23:34] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:27:40] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:28:19] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:28:20] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:28:32] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:28:34] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:31:20] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:31:32] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:31:59] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:32:02] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:48:10] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:55:35] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:55:49] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:57:29] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 00:02:48] [ERROR] {"message":"Version API invalide"}
[2025-03-09 00:06:00] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 01:30:19] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-09 01:30:19] [ERROR] {"message":"Version API invalide"}
[2025-03-09 01:48:05] [ERROR] {"message":"Version API invalide"}
[2025-03-09 02:00:56] [ERROR] {"message":"Version API invalide"}
[2025-03-09 02:02:46] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 11:48:46] [ERROR] {"message":"Version API invalide"}
[2025-03-09 12:02:39] [ERROR] {"message":"Version API invalide"}
[2025-03-09 17:16:21] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:16:23] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 17:25:37] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:26:52] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:27:26] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:35:23] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 19:23:27] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 19:25:19] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 19:26:46] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:33:31] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:33:36] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:35:40] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:35:52] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:53:30] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-10 00:46:10] [ERROR] {"message":"Version API invalide"}
[2025-03-10 14:39:17] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-10 21:18:55] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-10 21:30:09] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-10 21:49:18] [ERROR] {"message":"Version API invalide"}
[2025-03-10 23:21:13] [ERROR] {"message":"Version API invalide"}
[2025-03-10 23:41:30] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-11 00:52:23] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-11 00:52:47] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-11 20:51:37] [ERROR] {"message":"Version API invalide"}
[2025-03-11 21:03:47] [ERROR] {"message":"Version API invalide"}
[2025-03-11 21:05:08] [ERROR] {"message":"Version API invalide"}
[2025-03-12 00:05:54] [ERROR] {"message":"Version API invalide"}
[2025-03-12 00:38:33] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-12 21:57:42] [ERROR] {"message":"Version API invalide"}
[2025-03-12 22:27:40] [ERROR] {"message":"Version API invalide"}
[2025-03-12 22:28:16] [ERROR] {"message":"Version API invalide"}
[2025-03-13 01:36:48] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-13 01:45:10] [ERROR] {"message":"Version API invalide"}
[2025-03-13 02:44:17] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-13 02:44:17] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-13 02:57:55] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-13 02:57:55] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-13 21:29:11] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-13 21:29:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-14 01:05:41] [ERROR] {"message":"Version API invalide"}
[2025-03-14 17:51:04] [ERROR] {"message":"Version API invalide"}
[2025-03-15 19:21:15] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-03-15 19:24:09] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-03-16 15:59:48] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 16:00:12] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:19:13] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:26:36] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:26:44] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:26:47] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:29:38] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:03:24] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:06:20] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:30:33] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:35:57] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:49:04] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 22:00:41] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-16 22:00:42] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 22:00:44] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-16 22:00:51] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-16 22:01:53] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 22:04:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 15:32:28] [ERROR] {"message":"Version API invalide"}
[2025-03-17 17:28:36] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:29:36] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:30:41] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:32:58] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:34:57] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:35:36] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:36:09] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:36:29] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:37:30] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:50:01] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-18 20:39:31] [ERROR] {"message":"Version API invalide"}
[2025-03-18 20:51:45] [ERROR] {"message":"Version API invalide"}
[2025-03-19 00:51:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 00:52:19] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 00:52:38] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 02:57:05] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 02:58:50] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 13:25:26] [ERROR] {"message":"Version API invalide"}
[2025-03-19 13:25:50] [ERROR] {"message":"Version API invalide"}
[2025-03-19 18:22:12] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 18:30:19] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-20 13:22:27] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 00:02:04] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 00:04:31] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 14:10:50] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 23:44:06] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 02:23:19] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-03-24 02:25:24] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 02:26:05] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 02:26:48] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 19:46:09] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-28 10:14:14] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:14] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:14] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:35] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:35] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:15:25] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:15:25] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:15:25] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:17:58] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:19:45] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:19:45] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:20:55] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:20:55] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:21:39] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:23:18] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:23:18] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-04-06 16:27:20] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-04-22 10:43:58] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-10 13:01:49] [ERROR] {"message":"Version API invalide"}
[2025-05-10 13:01:50] [ERROR] {"message":"Version API invalide"}
[2025-05-10 19:07:59] [ERROR] {"message":"Version API invalide"}
[2025-05-10 19:16:06] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-10 19:55:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 22:50:49] [ERROR] {"message":"Version API invalide"}
[2025-05-14 22:57:22] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:03:47] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:05:43] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:18:47] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:18:52] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:19:10] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-05-14 23:19:21] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:21:06] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:35:27] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:40:33] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-05-14 23:40:41] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:45:30] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:47:34] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:53:08] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 00:00:10] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 00:02:52] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 00:26:48] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 01:00:09] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 01:26:28] [ERROR] {"message":"Version API invalide"}
[2025-05-15 02:06:23] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 03:25:30] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 13:11:28] [ERROR] {"message":"Version API invalide"}
[2025-06-05 03:56:15] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 03:56:15] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 03:59:18] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:00:17] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:00:19] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:04:14] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:05:38] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:09:46] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:13:44] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:13:56] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:13:57] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:14:00] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:14:02] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:14:02] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:14:17] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:14:20] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:14:23] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:14:25] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:15:01] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:20:22] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:20:23] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:21:29] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:21:50] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:21:52] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-05 04:22:02] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 04:22:03] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-05 04:22:21] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:22:22] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-05 04:22:27] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-05 04:22:32] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 04:22:32] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-05 04:26:11] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:26:22] [ERROR] {"message":"Erreur interne du serveur","error":"Failed opening required 'C:\\laragon\\www\\bus-booking\\api\\src\\Controllers\/..\/config\/database.php' (include_path='.;C:\/laragon\/etc\/php\/pear')"}
[2025-06-05 04:26:23] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:26:23] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:26:43] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-06-05 04:28:24] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 04:28:24] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-05 04:28:29] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:28:33] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:29:15] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:29:16] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:30:24] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:30:30] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 04:30:31] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-05 04:30:38] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:33:18] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 04:34:36] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:38:48] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:38:57] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:38:59] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:41:12] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:41:32] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:42:10] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:43:56] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:44:33] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 04:44:53] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 11:44:17] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 11:52:58] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 11:52:59] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-05 13:38:28] [ERROR] {"message":"Erreur interne du serveur","error":"Failed opening required 'C:\\laragon\\www\\bus-booking\\api\\src\\Controllers\/..\/config\/database.php' (include_path='.;C:\/laragon\/etc\/php\/pear')"}
[2025-06-05 13:38:28] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 13:38:28] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/bus-booking\/api\/index.php","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 13:59:55] [ERROR] {"message":"Erreur interne du serveur","error":"Failed opening required 'C:\\laragon\\www\\bus-booking\\api\\src\\Controllers\/..\/config\/database.php' (include_path='.;C:\/laragon\/etc\/php\/pear')"}
[2025-06-05 20:26:44] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-05 20:27:05] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 20:27:05] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-05 20:39:09] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 21:40:30] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 21:40:56] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 21:41:30] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 21:42:16] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 21:42:51] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 22:23:04] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 22:23:47] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-05 22:36:25] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-05 23:04:14] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-05 23:04:22] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-06-05 23:39:55] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-06-05 23:44:40] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-06-05 23:48:08] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-05 23:54:47] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripDetails(): Argument #1 ($trip_id) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 00:01:37] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripDetails(): Argument #1 ($trip_id) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 00:01:45] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripDetails(): Argument #1 ($trip_id) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 00:01:51] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripStops(): Argument #1 ($tripId) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 00:01:52] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/seats?route_id=1&bus_id=1&estimated_departure_time=2025-06-30%2008:00:00&estimated_arrival_time=2025-06-30%2015:30:00","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-06 00:13:00] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/seats?route_id=1&bus_id=1&bus_type=standard&estimated_departure_time=2025-05-30%2007:00:00&estimated_arrival_time=2025-05-30%2017:30:00","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-06 00:35:02] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/seats?route_id=1&bus_id=1&estimated_departure_time=2025-06-30%2007:00:00&estimated_arrival_time=2025-06-30%2017:30:00","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-06 00:53:03] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripSeats"}
[2025-06-06 01:59:10] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripSeats"}
[2025-06-06 02:01:56] [ERROR] {"message":"Erreur interne du serveur","error":"TripModel::getTripById(): Argument #1 ($trip_id) must be of type int, null given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Controllers\\TripController.php on line 254"}
[2025-06-06 02:22:03] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripStops(): Argument #1 ($tripId) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 02:26:00] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripStops(): Argument #1 ($tripId) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 02:31:25] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripStops(): Argument #1 ($tripId) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 02:32:16] [ERROR] {"message":"Erreur interne du serveur","error":"TripController::getTripStops(): Argument #1 ($tripId) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 167"}
[2025-06-06 03:32:31] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-06 03:32:39] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-06 03:32:43] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-06 03:32:48] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-06 03:34:46] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-06 03:34:47] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-06 03:35:25] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-06 03:35:36] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-07 21:29:57] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-07 21:29:58] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-07 21:30:30] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-07 21:31:08] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-07 21:37:47] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips?date=2025-06-30&from=1&to=2","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-08 00:09:05] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/locations\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-08 02:02:17] [ERROR] {"message":"Cet email est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-08 02:03:29] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-08 05:18:40] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 't.destination_location_id' in 'on clause'"}
[2025-06-08 05:19:29] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 't.destination_location_id' in 'on clause'"}
[2025-06-08 05:20:11] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 't.destination_location_id' in 'on clause'"}
[2025-06-08 05:45:07] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-08 05:45:16] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-08 05:58:39] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 't.destination_location_id' in 'on clause'"}
[2025-06-08 13:38:36] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-08 13:41:22] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 13:41:51] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 13:41:54] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-08 13:42:04] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 13:42:08] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-08 13:42:20] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-08 13:43:14] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 13:44:29] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 13:44:40] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 13:46:29] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-08 13:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 13:56:12] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 13:56:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 13:59:01] [ERROR] {"message":"Ce num\u00e9ro de t\u00e9l\u00e9phone est d\u00e9j\u00e0 utilis\u00e9"}
[2025-06-08 13:59:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 14:07:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 14:18:34] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 17:18:15] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-08 17:18:28] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 17:21:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 17:45:30] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 19:43:28] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 't.destination_location_id' in 'on clause'"}
[2025-06-08 19:54:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 20:08:15] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 20:08:26] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 20:27:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 20:29:17] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 20:31:20] [ERROR] {"message":"Email ou mot de passe incorrect"}
[2025-06-08 20:31:51] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-08 20:32:10] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 20:32:12] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-08 20:32:15] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 20:32:16] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-08 20:41:17] [ERROR] {"message":"Erreur interne du serveur","error":"Failed opening required 'C:\\laragon\\www\\bus-booking\\api\\src\\Controllers\/..\/config\/database.php' (include_path='.;C:\/laragon\/etc\/php\/pear')"}
[2025-06-08 20:42:45] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 't.destination_location_id' in 'on clause'"}
[2025-06-08 20:52:34] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 20:58:17] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-08 20:58:26] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-08 21:15:28] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-08 21:16:07] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-08 21:16:11] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-08 21:32:32] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-08 21:39:35] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:39:45] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:40:15] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:40:28] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:40:35] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:40:51] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:40:58] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:41:04] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:42:25] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 21:44:38] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-08 21:46:56] [ERROR] {"message":"Le champ 'trip_id' doit \u00eatre un entier positif"}
[2025-06-08 21:48:06] [ERROR] {"message":"Le champ 'trip_id' doit \u00eatre un entier positif"}
[2025-06-08 21:48:40] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-08 22:00:32] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 22:00:33] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 22:00:34] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 22:00:34] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-08 22:00:35] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 22:00:37] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 22:00:38] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-08 22:01:54] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-08 22:03:40] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-08 22:03:46] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-09 00:56:15] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-09 01:18:13] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-09 01:18:50] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-09 02:18:53] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-09 03:21:18] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-09 03:21:40] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-09 03:22:10] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-06-09 11:36:35] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 't.destination_location_id' in 'on clause'"}
[2025-06-09 11:47:25] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-06-09 12:14:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-09 12:15:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-09 20:36:34] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-09 20:37:38] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-09 20:38:07] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-10 23:36:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-10 23:41:21] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-10 23:41:57] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-11 00:43:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-11 03:00:51] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [1049] Unknown database 'bus_booking'"}
[2025-06-11 03:16:08] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [1049] Unknown database 'bus_booking'"}
[2025-06-11 03:22:30] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [1049] Unknown database 'bus_booking'"}
[2025-06-11 15:12:03] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [1049] Unknown database 'bus_booking'"}
[2025-06-11 15:12:24] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [1049] Unknown database 'bus_booking'"}
[2025-06-11 17:42:50] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-06-11 17:43:28] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-06-11 17:43:52] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-06-11 19:49:02] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-11 19:52:30] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-11 19:55:23] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-11 19:56:28] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getAmenities"}
[2025-06-11 19:56:34] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getAmenities"}
[2025-06-11 19:56:49] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-11 19:56:57] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-11 19:57:17] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getAmenities"}
[2025-06-11 19:59:00] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-11 19:59:11] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-11 19:59:28] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-11 19:59:32] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getBookingsForOperator()"}
[2025-06-11 20:37:18] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 20:39:45] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getTotalBookings()"}
[2025-06-11 23:05:38] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:09:26] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 23:09:29] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 23:09:43] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 23:10:01] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 23:10:42] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:25:27] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 23:25:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-11 23:26:11] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 23:27:10] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:35:17] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-11 23:44:23] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:44:38] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:45:39] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:55:08] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:56:11] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-11 23:56:57] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:14:54] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:21:42] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:27:57] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:35:08] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:39:32] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:40:33] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:33] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:34] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:34] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:34] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:34] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:35] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:35] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:35] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:40:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:35] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:35] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:35] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:35] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:36] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:37] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:38] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:39] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:45:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:05] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:05] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:05] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:06] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:06] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:06] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:06] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:06] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:06] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:07] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:07] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:07] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:07] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:07] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:09] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:40] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:41] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:42] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:43] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:44] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:45] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:46] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:47] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:48] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:49] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:50] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:51] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:52] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:53] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:54] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:55] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:56] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:57] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:58] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:46:59] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:00] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:01] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:02] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:03] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:04] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:04] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:04] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:08] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 00:47:18] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:53:58] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 00:54:07] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:55:33] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:57:25] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 00:57:32] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:02:53] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:03:02] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:04:18] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:04:19] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:08:50] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:09:50] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:15:55] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'where clause'"}
[2025-06-12 01:32:35] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getRecentBookingsForOperator()"}
[2025-06-12 01:40:02] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getPopularRoutes()"}
[2025-06-12 01:42:06] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getPopularRoutes()"}
[2025-06-12 01:55:15] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''20' OFFSET '0'' at line 4"}
[2025-06-12 01:58:18] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getPopularRoutes()"}
[2025-06-12 01:58:35] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getPopularRoutes()"}
[2025-06-12 02:03:16] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getPopularRoutes()"}
[2025-06-12 02:06:45] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getPopularRoutes()"}
[2025-06-12 02:09:07] [ERROR] {"message":"Erreur interne du serveur","error":"Call to undefined method BookingModel::getPeakBookingTimes()"}
[2025-06-12 02:11:37] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''20' OFFSET '0'' at line 4"}
[2025-06-12 02:19:48] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 02:20:14] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-12 02:20:59] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 02:21:16] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 02:23:17] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 02:23:34] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-12 02:24:05] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-12 03:09:48] [ERROR] {"message":"Erreur de routage","error":"Route '' does not exist."}
[2025-06-12 03:09:48] [ERROR] {"message":"Erreur de routage","error":"Route '' does not exist."}
[2025-06-12 03:10:27] [ERROR] {"message":"Erreur de routage","error":"Route '' does not exist."}
[2025-06-12 03:12:00] [ERROR] {"message":"Erreur de routage","error":"Route '' does not exist."}
[2025-06-12 03:14:22] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 03:14:41] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 03:14:57] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 03:19:35] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 03:21:56] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 03:24:09] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 03:28:08] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'created_at' in 'field list'"}
[2025-06-12 03:28:16] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 03:39:28] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTrips"}
[2025-06-12 04:45:18] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ''20' OFFSET '0'' at line 4"}
[2025-06-12 12:35:55] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 12:36:01] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 12:36:09] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 12:36:12] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 12:36:16] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 12:36:27] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 20:42:29] [ERROR] {"message":"Token invalide"}
[2025-06-12 20:42:29] [ERROR] {"message":"Token invalide"}
[2025-06-12 20:42:59] [ERROR] {"message":"Token invalide"}
[2025-06-12 20:42:59] [ERROR] {"message":"Token invalide"}
[2025-06-12 22:04:43] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 23:54:20] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/trips?date=2025-06-30&from=1&to=2","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 23:54:37] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/search?date=2025-06-30&from=1&to=2","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 23:56:30] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/seats?route_id=1&bus_id=1&bus_type=standard&estimated_departure_time=2025-05-30%2007:00:00&estimated_arrival_time=2025-05-30%2017:30:00","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 23:58:07] [ERROR] {"message":"Erreur interne du serveur","error":"Class \"AuthMiddleware\" not found"}
[2025-06-12 23:58:26] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-12 23:59:05] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/login","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 23:59:24] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/users\/login","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-12 23:59:48] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/auth\/login","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 00:03:25] [ERROR] {"message":"Token invalide"}
[2025-06-13 02:20:12] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/seat-plans","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 02:22:46] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/pricing","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 02:23:03] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/pricing","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 02:23:08] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/seat-plans","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 02:24:51] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/seat-plans","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 02:34:21] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/seat-plans","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 02:55:22] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/seat-plans","method":"POST","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 07:54:00] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 07:54:30] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 16:58:11] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 16:58:59] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 17:07:29] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 17:08:30] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 17:08:54] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 17:08:58] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/buses\/1\/","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 17:29:37] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getBusById"}
[2025-06-13 20:04:23] [ERROR] {"message":"Token invalide"}
[2025-06-13 20:04:23] [ERROR] {"message":"Token invalide"}
[2025-06-13 20:05:54] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getBusById"}
[2025-06-13 20:22:52] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getBusDetails"}
[2025-06-13 20:23:09] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/locations\/1","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-13 20:24:05] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'location_name' cannot be null"}
[2025-06-13 21:21:29] [ERROR] {"message":"Erreur interne du serveur","error":"PDOStatement::bindParam(): Argument #2 ($var) could not be passed by reference"}
[2025-06-13 21:21:48] [ERROR] {"message":"Erreur interne du serveur","error":"PDOStatement::bindParam(): Argument #2 ($var) could not be passed by reference"}
[2025-06-13 22:13:02] [ERROR] {"message":"Token invalide"}
[2025-06-13 22:13:02] [ERROR] {"message":"Token invalide"}
[2025-06-13 22:13:15] [ERROR] {"message":"Token invalide"}
[2025-06-13 22:57:56] [ERROR] {"message":"Le champ 'seat_number' est requis"}
[2025-06-13 22:58:10] [ERROR] {"message":"Le champ 'seat_number' est requis"}
[2025-06-14 00:17:23] [ERROR] {"message":"Le champ 'seat_number' est requis"}
[2025-06-14 00:38:44] [ERROR] {"message":"Le champ 'seat_number' est requis"}
[2025-06-14 01:49:54] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'price' in 'field list'"}
[2025-06-14 22:23:08] [ERROR] {"message":"Token invalide"}
[2025-06-14 22:23:16] [ERROR] {"message":"Token invalide"}
[2025-06-14 22:23:16] [ERROR] {"message":"Token invalide"}
[2025-06-14 22:41:17] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-14 23:15:02] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/operator\/locations","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-14 23:18:41] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/operator\/locations","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-15 16:50:40] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/trips\/7\/details","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-15 16:51:28] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripDetails"}
[2025-06-15 16:51:33] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripDetails"}
[2025-06-15 16:58:10] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getRouteStops"}
[2025-06-15 16:58:54] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getRouteStops"}
[2025-06-15 16:58:59] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getRouteStops"}
[2025-06-15 17:14:43] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getRouteStops"}
[2025-06-15 17:15:50] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripStops"}
[2025-06-15 17:27:15] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripStops"}
[2025-06-15 17:30:33] [ERROR] {"message":"Erreur interne du serveur","error":"Cannot use object of type stdClass as array"}
[2025-06-15 17:31:20] [ERROR] {"message":"Erreur interne du serveur","error":"Cannot use object of type stdClass as array"}
[2025-06-15 17:40:17] [ERROR] {"message":"ID bus requis"}
[2025-06-15 20:53:34] [ERROR] {"message":"Token invalide"}
[2025-06-15 20:53:40] [ERROR] {"message":"Token invalide"}
[2025-06-15 21:04:40] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripDetails"}
[2025-06-15 22:03:50] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripStops"}
[2025-06-15 22:36:11] [ERROR] {"message":"Token invalide"}
[2025-06-15 22:36:12] [ERROR] {"message":"Token invalide"}
[2025-06-15 22:53:37] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripStops"}
[2025-06-16 02:26:17] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getRouteStops"}
[2025-06-16 02:27:41] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getRouteStops"}
[2025-06-16 02:28:17] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getRouteStops"}
[2025-06-16 02:32:00] [ERROR] {"message":"Erreur interne du serveur","error":"RouteController::getRouteStops(): Argument #1 ($routeId) must be of type int, array given, called in C:\\laragon\\www\\bus-booking\\api\\src\\Router\\Router.php on line 255"}
[2025-06-16 02:44:26] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: createRouteStop"}
[2025-06-16 02:47:41] [ERROR] {"message":"M\u00e9thode non trouv\u00e9e: getTripStops"}
[2025-06-16 04:44:19] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 04:45:58] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 04:48:07] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 04:48:34] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 04:55:27] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 04:57:03] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 05:08:12] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 05:12:39] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 05:15:25] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 12:04:07] [ERROR] {"message":"Erreur interne du serveur","error":"Cannot use object of type stdClass as array"}
[2025-06-16 12:04:19] [ERROR] {"message":"Erreur interne du serveur","error":"Cannot use object of type stdClass as array"}
[2025-06-16 12:10:41] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect time value: '2h15' for column 'duration' at row 1"}
[2025-06-16 12:27:17] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 12:29:13] [ERROR] {"message":"Erreur interne du serveur","error":"Cannot use object of type stdClass as array"}
[2025-06-16 12:29:26] [ERROR] {"message":"Erreur interne du serveur","error":"Cannot use object of type stdClass as array"}
[2025-06-16 12:33:01] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 12:35:19] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 12:49:50] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 12:50:19] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 13:05:44] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 13:06:01] [ERROR] {"message":"Erreur interne du serveur","error":"Cannot use object of type stdClass as array"}
[2025-06-16 13:12:44] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 13:15:14] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 13:16:06] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 13:17:22] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 13:19:21] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 13:24:40] [ERROR] {"message":"Erreur serveur","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ur.role_name' in 'field list'"}
[2025-06-16 14:33:23] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/auth\/me","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:33:23] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:33:23] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:33:23] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:33:23] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:33:24] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:33:54] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:33:54] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:33:54] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:33:54] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:33:54] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:37:34] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:37:35] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:37:35] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:37:35] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:37:35] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:50:03] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:50:03] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:50:03] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:50:03] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:50:03] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:50:13] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:50:14] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:50:14] [ERROR] {"error":"Jeton d'authentification manquant ou a expir\u00e9. Veuillez vous reconnecter."}
[2025-06-16 14:50:14] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:50:15] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:50:22] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:50:22] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:51:54] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:51:54] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:55:29] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:55:29] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:55:29] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:55:29] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:55:29] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:55:29] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:55:56] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:56:06] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:56:06] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:56:06] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:56:06] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:56:07] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:56:20] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:56:20] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:56:28] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:56:28] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:56:28] [ERROR] {"message":"Token invalide"}
[2025-06-16 14:56:28] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/drivers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:56:28] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/operator\/controllers","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 14:56:29] [ERROR] {"message":"Token invalide"}
[2025-06-16 15:27:28] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/operator\/dashboard\/stats","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 15:27:29] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/operator\/dashboard\/trips-today","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 15:27:29] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/operator\/dashboard\/alerts","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 15:27:29] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/operator\/dashboard\/revenue","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 15:48:30] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/\/operator\/dashboard\/stats","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 15:48:31] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/\/operator\/dashboard\/revenue","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 15:48:31] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/\/operator\/dashboard\/trips-today","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
[2025-06-16 15:48:31] [ERROR] {"message":"Endpoint non trouv\u00e9","request_uri":"\/api\/v1\/\/operator\/dashboard\/alerts","method":"GET","available_endpoints":["GET \/v1\/test","GET \/v1\/health","GET \/v1\/locations","GET \/v1\/routes","GET \/v1\/trips","GET \/v1\/amenities"]}
