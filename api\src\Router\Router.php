<?php
require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../Helpers/response.php';

class Router {
    private $router;

    public function __construct() {
        $this->router = new AltoRouter();
        $this->setupRoutes();
    }
    
    private function setupRoutes() {
        // Détecter automatiquement le basePath
        $scriptName = $_SERVER['SCRIPT_NAME'];
        $basePath = dirname($scriptName);

        // Si on est dans un sous-dossier, ajuster le basePath
        if (strpos($_SERVER['REQUEST_URI'], '/bus-booking/') !== false) {
            $basePath = '/bus-booking/api';
        } else {
            $basePath = '/api';
        }

        $this->router->setBasePath($basePath);

        // Route de test
        $this->router->map('GET', '/v1/test', 'TestController#test');
        $this->router->map('GET', '/v1/health', 'TestController#health');

        // Routes d'authentification
        $this->router->map('POST', '/v1/auth/register', 'UserController#register');
        $this->router->map('POST', '/v1/auth/login', 'UserController#login');
        $this->router->map('POST', '/v1/auth/logout', 'UserController#logout');
        $this->router->map('POST', '/v1/auth/refresh', 'UserController#refreshToken');

        // Routes utilisateurs
        $this->router->map('GET', '/v1/users/profile', 'UserController#getProfile');
        $this->router->map('PUT', '/v1/users/profile', 'UserController#updateProfile');
        $this->router->map('GET', '/v1/users/dashboard', 'UserController#getDashboard');

        // Routes de localisation
        $this->router->map('GET', '/v1/locations', 'LocationController#getLocations');
        $this->router->map('GET', '/v1/locations/[i:id]', 'LocationController#getLocationById');

        // Routes des trajets
        $this->router->map('GET', '/v1/routes', 'RouteController#getAllRoutes');
        $this->router->map('GET', '/v1/routes/[i:id]', 'RouteController#getRouteById');
        $this->router->map('POST', '/v1/routes', 'RouteController#createRoute');
        $this->router->map('PUT', '/v1/routes/[i:id]', 'RouteController#updateRoute');
        $this->router->map('DELETE', '/v1/routes/[i:id]', 'RouteController#deleteRoute');

        // Routes des voyages
        $this->router->map('GET', '/v1/trips/search', 'TripController#searchTrips');
        $this->router->map('GET', '/v1/trips/[i:id]', 'TripController#getTripDetails');
        $this->router->map('GET', '/v1/trips/[i:id]/stops', 'TripController#getTripStops');
        $this->router->map('GET', '/v1/trips/[i:id]/seats', 'TripController#getTripSeats');
        $this->router->map('POST', '/v1/trips', 'TripController#createTrip');
        $this->router->map('PUT', '/v1/trips/[i:id]', 'TripController#updateTrip');
        $this->router->map('DELETE', '/v1/trips/[i:id]', 'TripController#cancelTrip');
        
        // Routes des réservations
        $this->router->map('GET', '/v1/bookings', 'BookingController#getUserBookings');
        $this->router->map('GET', '/v1/bookings/[i:id]', 'BookingController#getBookingById');
        $this->router->map('POST', '/v1/bookings', 'BookingController#createBooking');
        $this->router->map('PUT', '/v1/bookings/[i:id]', 'BookingController#updateBooking');
        $this->router->map('DELETE', '/v1/bookings/[i:id]', 'BookingController#cancelBooking');

        $this->router->map('GET', '/v1/bookings/search', 'BookingController#searchBookings');

        // Routes des paiements
        $this->router->map('POST', '/v1/payments', 'PaymentController#createPayment');
        $this->router->map('GET', '/v1/payments/[i:id]', 'PaymentController#getPaymentStatus');
        $this->router->map('POST', '/v1/payments/fedapay/webhook', 'PaymentController#handleFedaPayWebhook');

        // Routes des équipements
        $this->router->map('GET', '/v1/amenities', 'BusController#getAllAmenities');
        
        // Routes des bus (opérateurs)
        $this->router->map('GET', '/v1/buses', 'BusController#getBuses');
        $this->router->map('GET', '/v1/buses/[i:id]', 'BusController#getBusDetails');
        $this->router->map('POST', '/v1/buses', 'BusController#createBus');
        $this->router->map('PUT', '/v1/buses/[i:id]', 'BusController#updateBus');
        $this->router->map('DELETE', '/v1/buses/[i:id]', 'BusController#deleteBus');

        // Routes des tickets
        $this->router->map('GET', '/v1/tickets/[*:code]', 'TicketController#getTicketByCode');
        $this->router->map('POST', '/v1/tickets/validate', 'TicketController#validateTicket');
        $this->router->map('GET', '/v1/tickets/[i:id]/qr', 'TicketController#generateQRCode');
        $this->router->map('GET', '/v1/bookings/[i:booking_id]/tickets', 'TicketController#getBookingTickets');

        // Routes du tableau de bord opérateur
        $this->router->map('GET', '/v1/operator/dashboard', 'OperatorController#getDashboard');
        $this->router->map('GET', '/v1/operator/bookings', 'OperatorController#getBookings');
        $this->router->map('GET', '/v1/operator/reports', 'OperatorController#getReports');
        $this->router->map('GET', '/v1/operator/analytics', 'OperatorController#getAnalytics');

        // Routes CRUD pour l'opérateur - Lieux
        $this->router->map('GET', '/v1/operator/locations', 'OperatorController#getLocations');
        $this->router->map('POST', '/v1/operator/locations', 'OperatorController#createLocation');
        $this->router->map('PUT', '/v1/operator/locations/[i:id]', 'OperatorController#updateLocation');
        $this->router->map('DELETE', '/v1/operator/locations/[i:id]', 'OperatorController#deleteLocation');

        // Routes CRUD pour l'opérateur - Arrêts
        $this->router->map('GET', '/v1/operator/stops', 'OperatorController#getStops');
        $this->router->map('POST', '/v1/operator/stops', 'OperatorController#createStop');
        $this->router->map('PUT', '/v1/operator/stops/[i:id]', 'OperatorController#updateStop');
        $this->router->map('DELETE', '/v1/operator/stops/[i:id]', 'OperatorController#deleteStop');

        // Routes CRUD pour l'opérateur - Bus
        $this->router->map('GET', '/v1/operator/buses', 'OperatorController#getBuses');
        $this->router->map('GET', '/v1/operator/buses/[i:id]', 'OperatorController#getBusDetails');
        $this->router->map('POST', '/v1/operator/buses', 'OperatorController#createBus');
        $this->router->map('PUT', '/v1/operator/buses/[i:id]', 'OperatorController#updateBus');
        $this->router->map('DELETE', '/v1/operator/buses/[i:id]', 'OperatorController#deleteBus');

        // Route pour les plans de sièges (utilisée par manageBusSeats)
        $this->router->map('GET', '/v1/operator/seat-plans/[i:id]', 'OperatorController#getSeatPlanDetails');

        // Routes CRUD pour l'opérateur - Sièges
        $this->router->map('GET', '/v1/operator/buses/[i:bus_id]/seats', 'OperatorController#getBusSeats');
        $this->router->map('POST', '/v1/operator/buses/[i:bus_id]/seats', 'OperatorController#saveSeatsForBus');
        $this->router->map('PUT', '/v1/operator/seats/[i:id]', 'OperatorController#updateSeat');
        $this->router->map('DELETE', '/v1/operator/seats/[i:id]', 'OperatorController#deleteSeat');

        // Routes CRUD pour l'opérateur - Commodités
        $this->router->map('GET', '/v1/operator/amenities', 'OperatorController#getAmenities');
        $this->router->map('POST', '/v1/operator/amenities', 'OperatorController#createAmenity');
        $this->router->map('PUT', '/v1/operator/amenities/[i:id]', 'OperatorController#updateAmenity');
        $this->router->map('DELETE', '/v1/operator/amenities/[i:id]', 'OperatorController#deleteAmenity');

        // Routes CRUD pour l'opérateur - Trajets
        $this->router->map('GET', '/v1/operator/routes', 'OperatorController#getRoutes');
        $this->router->map('POST', '/v1/operator/routes', 'OperatorController#createRoute');
        $this->router->map('PUT', '/v1/operator/routes/[i:id]', 'OperatorController#updateRoute');
        $this->router->map('DELETE', '/v1/operator/routes/[i:id]', 'OperatorController#deleteRoute');

        // Routes CRUD pour l'opérateur - Voyages
        $this->router->map('GET', '/v1/operator/trips', 'OperatorController#getTrips');
        $this->router->map('POST', '/v1/operator/trips', 'OperatorController#createTrip');
        $this->router->map('PUT', '/v1/operator/trips/[i:id]', 'OperatorController#updateTrip');
        $this->router->map('DELETE', '/v1/operator/trips/[i:id]', 'OperatorController#deleteTrip');

        // Routes CRUD pour l'opérateur - Tarification
        $this->router->map('GET', '/v1/operator/pricing', 'OperatorController#getPricing');
        $this->router->map('POST', '/v1/operator/pricing', 'OperatorController#createPricing');
        $this->router->map('PUT', '/v1/operator/pricing/[i:id]', 'OperatorController#updatePricing');
        $this->router->map('DELETE', '/v1/operator/pricing/[i:id]', 'OperatorController#deletePricing');

        // Routes CRUD pour l'opérateur - Paiements
        $this->router->map('GET', '/v1/operator/payments', 'OperatorController#getPayments');
        $this->router->map('POST', '/v1/operator/payments/[i:id]/refund', 'OperatorController#refundPayment');

        // Routes CRUD pour l'opérateur - Utilisateurs
        $this->router->map('GET', '/v1/operator/users', 'OperatorController#getUsers');
        $this->router->map('POST', '/v1/operator/users', 'OperatorController#createUser');
        $this->router->map('PUT', '/v1/operator/users/[i:id]', 'OperatorController#updateUser');
        $this->router->map('DELETE', '/v1/operator/users/[i:id]', 'OperatorController#deleteUser');

        // Routes CRUD pour l'opérateur - Sièges (liste générale)
        $this->router->map('GET', '/v1/operator/seats', 'OperatorController#getSeats');

        // Routes CRUD pour l'opérateur - Plans de sièges
        $this->router->map('GET', '/v1/operator/seat-plans', 'OperatorController#getSeatPlans');
        $this->router->map('POST', '/v1/operator/seat-plans', 'OperatorController#createSeatPlan');
        $this->router->map('PUT', '/v1/operator/seat-plans/[i:id]', 'OperatorController#updateSeatPlan');
        $this->router->map('DELETE', '/v1/operator/seat-plans/[i:id]', 'OperatorController#deleteSeatPlan');

        // Routes des notifications
        $this->router->map('POST', '/v1/notifications/email', 'NotificationController#sendEmail');
        $this->router->map('GET', '/v1/notifications/templates', 'NotificationController#getTemplates');
    }
    
    public function dispatch() {
        // Mode debug
        if (isset($_GET['debug'])) {
            $this->debugRequest();
            return;
        }

        $match = $this->router->match();

        if ($match === false) {
            sendResponse(404, [
                'message' => 'Endpoint non trouvé',
                'request_uri' => $_SERVER['REQUEST_URI'],
                'method' => $_SERVER['REQUEST_METHOD'],
                'available_endpoints' => [
                    'GET /v1/test',
                    'GET /v1/health',
                    'GET /v1/locations',
                    'GET /v1/routes',
                    'GET /v1/trips',
                    'GET /v1/amenities'
                ]
            ]);
            return;
        }

        list($controller, $action) = explode('#', $match['target']);
        $params = $match['params'];

        // Authentification unique pour les routes opérateur
        $requestPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        if (strpos($requestPath, '/v1/operator/') !== false) {
            require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';
            AuthMiddleware::authenticate('operator');
        }

        $controllerFile = __DIR__ . "/../Controllers/{$controller}.php";
        if (!file_exists($controllerFile)) {
            sendResponse(500, ['message' => 'Contrôleur non trouvé: ' . $controller]);
            return;
        }

        require_once $controllerFile;

        if (!class_exists($controller)) {
            sendResponse(500, ['message' => 'Classe contrôleur non trouvée: ' . $controller]);
            return;
        }

        $controllerInstance = new $controller();

        if (!method_exists($controllerInstance, $action)) {
            sendResponse(500, ['message' => 'Méthode non trouvée: ' . $action]);
            return;
        }

        // Récupérer les données de la requête
        $requestBody = file_get_contents('php://input');
        $data = !empty($requestBody) ? json_decode($requestBody, true) : [];
        $queryParams = $_GET ?? [];

        // Fusionner les paramètres d'URL avec les données
        $allParams = array_merge($params, $data ?? [], $queryParams);

        try {
            // Appeler la méthode du contrôleur avec les paramètres
            $controllerInstance->$action($allParams);
        } catch (Exception $e) {
            error_log("Erreur dans {$controller}::{$action}: " . $e->getMessage());
            sendResponse(500, [
                'message' => 'Erreur interne du serveur',
                'error' => $_ENV['APP_DEBUG'] === 'true' ? $e->getMessage() : 'Une erreur est survenue'
            ]);
        }
    }

    private function debugRequest() {
        $debug = [
            'message' => 'Mode debug du routeur',
            'request_info' => [
                'uri' => $_SERVER['REQUEST_URI'],
                'method' => $_SERVER['REQUEST_METHOD'],
                'script_name' => $_SERVER['SCRIPT_NAME'],
                'path_info' => $_SERVER['PATH_INFO'] ?? 'N/A',
                'query_string' => $_SERVER['QUERY_STRING'] ?? 'N/A'
            ],
            'router_info' => [
                'base_path' => 'Configuré automatiquement',
                'current_path' => parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH)
            ],
            'available_endpoints' => [
                'GET /v1/test - Test de l\'API',
                'GET /v1/health - Vérification de santé',
                'GET /v1/locations - Liste des villes',
                'GET /v1/routes - Liste des trajets',
                'GET /v1/trips - Recherche de voyages',
                'GET /v1/amenities - Équipements des bus'
            ]
        ];

        sendResponse(200, $debug);
    }
}
