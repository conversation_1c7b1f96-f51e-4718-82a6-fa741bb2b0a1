<!-- Modal pour Lieu/Ville -->
<div class="modal fade" id="locationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="locationModalTitle">Nouveau Lieu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="locationForm">
                    <input type="hidden" id="locationId">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="locationName" class="form-label">Nom du lieu *</label>
                            <input type="text" class="form-control" id="locationName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="locationRegion" class="form-label">Région *</label>
                            <input type="text" class="form-control" id="locationRegion" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="locationCountry" class="form-label">Pays *</label>
                            <input type="text" class="form-control" id="locationCountry" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="locationTimeZone" class="form-label">Fuseau horaire *</label>
                            <select class="form-select" id="locationTimeZone" required>
                                <option value="">Sélectionner...</option>
                                <option value="Africa/Porto-Novo">Africa/Porto-Novo</option>
                                <option value="Africa/Abidjan">Africa/Abidjan</option>
                                <option value="Africa/Accra">Africa/Accra</option>
                                <option value="Africa/Bamako">Africa/Bamako</option>
                                <option value="Africa/Ouagadougou">Africa/Ouagadougou</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="locationLatitude" class="form-label">Latitude *</label>
                            <input type="number" step="any" class="form-control" id="locationLatitude" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="locationLongitude" class="form-label">Longitude *</label>
                            <input type="number" step="any" class="form-control" id="locationLongitude" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="locationStatus" class="form-label">Statut</label>
                        <select class="form-select" id="locationStatus">
                            <option value="active">Actif</option>
                            <option value="inactive">Inactif</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveLocation()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>
