/**
 * Module de chargement de pages pour l'interface opérateur
 * Gestion du chargement dynamique des sections et modales avec cache
 */

class PageLoader {
    constructor() {
        this.pageCache = new Map();
        this.modalCache = new Map();
        this.loadingPromises = new Map();
        this.basePath = '/operator/pages/';
        this.modalPath = '/operator/modals/';
    }

    /**
     * Charge une page avec cache
     */
    async loadPage(pageName) {
        // Vérifier le cache
        if (this.pageCache.has(pageName)) {
            return this.pageCache.get(pageName);
        }

        // Vérifier si un chargement est en cours
        if (this.loadingPromises.has(pageName)) {
            return this.loadingPromises.get(pageName);
        }

        // Charger la page
        const promise = this._fetchPage(pageName);
        this.loadingPromises.set(pageName, promise);

        try {
            const content = await promise;
            this.pageCache.set(pageName, content);
            this.loadingPromises.delete(pageName);
            return content;
        } catch (error) {
            this.loadingPromises.delete(pageName);
            throw error;
        }
    }

    async _fetchPage(pageName) {
        try {
            const response = await fetch(`${this.basePath}${pageName}.html`);
            if (!response.ok) {
                throw new Error(`Erreur ${response.status}: ${response.statusText}`);
            }
            return await response.text();
        } catch (error) {
            console.error(`Erreur lors du chargement de la page ${pageName}:`, error);
            throw new Error(`Impossible de charger la page ${pageName}`);
        }
    }

    /**
     * Charge une modale avec cache
     */
    async loadModal(modalName) {
        // Vérifier le cache
        if (this.modalCache.has(modalName)) {
            return this.modalCache.get(modalName);
        }

        // Vérifier si un chargement est en cours
        const loadingKey = `modal_${modalName}`;
        if (this.loadingPromises.has(loadingKey)) {
            return this.loadingPromises.get(loadingKey);
        }

        // Charger la modale
        const promise = this._fetchModal(modalName);
        this.loadingPromises.set(loadingKey, promise);

        try {
            const content = await promise;
            this.modalCache.set(modalName, content);
            this.loadingPromises.delete(loadingKey);
            return content;
        } catch (error) {
            this.loadingPromises.delete(loadingKey);
            throw error;
        }
    }

    async _fetchModal(modalName) {
        try {
            const response = await fetch(`${this.modalPath}${modalName}-modal.html`);
            if (!response.ok) {
                throw new Error(`Erreur ${response.status}: ${response.statusText}`);
            }
            return await response.text();
        } catch (error) {
            console.error(`Erreur lors du chargement de la modale ${modalName}:`, error);
            throw new Error(`Impossible de charger la modale ${modalName}`);
        }
    }

    /**
     * Charge plusieurs modales en parallèle
     */
    async loadModals(modalNames) {
        const promises = modalNames.map(name => this.loadModal(name));
        return Promise.all(promises);
    }

    /**
     * Injecte une modale dans le DOM si elle n'existe pas déjà
     */
    async injectModal(modalName) {
        const modalId = `${modalName}Modal`;
        
        // Vérifier si la modale existe déjà
        if (document.getElementById(modalId)) {
            return;
        }

        try {
            const modalContent = await this.loadModal(modalName);
            const modalContainer = document.getElementById('modal-container') || document.body;
            modalContainer.insertAdjacentHTML('beforeend', modalContent);
        } catch (error) {
            console.error(`Erreur lors de l'injection de la modale ${modalName}:`, error);
            throw error;
        }
    }

    /**
     * Injecte plusieurs modales dans le DOM
     */
    async injectModals(modalNames) {
        const promises = modalNames.map(name => this.injectModal(name));
        return Promise.all(promises);
    }

    /**
     * Supprime le cache d'une page
     */
    invalidatePage(pageName) {
        this.pageCache.delete(pageName);
    }

    /**
     * Supprime le cache d'une modale
     */
    invalidateModal(modalName) {
        this.modalCache.delete(modalName);
    }

    /**
     * Vide tout le cache
     */
    clearCache() {
        this.pageCache.clear();
        this.modalCache.clear();
        this.loadingPromises.clear();
    }

    /**
     * Précharge une page
     */
    async preloadPage(pageName) {
        try {
            await this.loadPage(pageName);
        } catch (error) {
            console.warn(`Impossible de précharger la page ${pageName}:`, error);
        }
    }

    /**
     * Précharge plusieurs pages
     */
    async preloadPages(pageNames) {
        const promises = pageNames.map(name => this.preloadPage(name));
        return Promise.allSettled(promises);
    }

    /**
     * Précharge une modale
     */
    async preloadModal(modalName) {
        try {
            await this.loadModal(modalName);
        } catch (error) {
            console.warn(`Impossible de précharger la modale ${modalName}:`, error);
        }
    }

    /**
     * Précharge plusieurs modales
     */
    async preloadModals(modalNames) {
        const promises = modalNames.map(name => this.preloadModal(name));
        return Promise.allSettled(promises);
    }
}

// Instance globale du chargeur de pages
window.pageLoader = new PageLoader();

// Fonction utilitaire pour créer un conteneur de modales si il n'existe pas
document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('modal-container')) {
        const modalContainer = document.createElement('div');
        modalContainer.id = 'modal-container';
        document.body.appendChild(modalContainer);
    }
});
