/**
 * Module de cache de données pour l'interface opérateur
 * Gestion du lazy loading et du cache en mémoire pour éviter les appels API redondants
 */

class DataCache {
    constructor() {
        this.cache = new Map();
        this.loadingPromises = new Map();
        this.cacheExpiry = new Map();
        this.defaultTTL = 5 * 60 * 1000; // 5 minutes par défaut
    }

    /**
     * Vérifie si une donnée est en cache et valide
     */
    isValid(key) {
        if (!this.cache.has(key)) return false;
        
        const expiry = this.cacheExpiry.get(key);
        if (expiry && Date.now() > expiry) {
            this.cache.delete(key);
            this.cacheExpiry.delete(key);
            return false;
        }
        
        return true;
    }

    /**
     * Met en cache une donnée avec TTL optionnel
     */
    set(key, data, ttl = this.defaultTTL) {
        this.cache.set(key, data);
        if (ttl > 0) {
            this.cacheExpiry.set(key, Date.now() + ttl);
        }
    }

    /**
     * Récupère une donnée du cache
     */
    get(key) {
        if (this.isValid(key)) {
            return this.cache.get(key);
        }
        return null;
    }

    /**
     * Supprime une entrée du cache
     */
    delete(key) {
        this.cache.delete(key);
        this.cacheExpiry.delete(key);
        this.loadingPromises.delete(key);
    }

    /**
     * Vide tout le cache
     */
    clear() {
        this.cache.clear();
        this.cacheExpiry.clear();
        this.loadingPromises.clear();
    }

    /**
     * Charge les lieux avec cache
     */
    async getLocations() {
        const key = 'locations';
        
        // Vérifier le cache
        const cached = this.get(key);
        if (cached) {
            return cached;
        }

        // Vérifier si un chargement est en cours
        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        // Charger les données
        const promise = this._loadLocations();
        this.loadingPromises.set(key, promise);

        try {
            const data = await promise;
            this.set(key, data);
            this.loadingPromises.delete(key);
            return data;
        } catch (error) {
            this.loadingPromises.delete(key);
            throw error;
        }
    }

    async _loadLocations() {
        const response = await apiRequest('operator/locations');
        return response.locations || [];
    }

    /**
     * Charge les arrêts avec cache
     */
    async getStops() {
        const key = 'stops';
        
        const cached = this.get(key);
        if (cached) return cached;

        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        const promise = this._loadStops();
        this.loadingPromises.set(key, promise);

        try {
            const data = await promise;
            this.set(key, data);
            this.loadingPromises.delete(key);
            return data;
        } catch (error) {
            this.loadingPromises.delete(key);
            throw error;
        }
    }

    async _loadStops() {
        const response = await apiRequest('operator/stops');
        return response.stops || [];
    }

    /**
     * Charge les bus avec cache
     */
    async getBuses() {
        const key = 'buses';
        
        const cached = this.get(key);
        if (cached) return cached;

        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        const promise = this._loadBuses();
        this.loadingPromises.set(key, promise);

        try {
            const data = await promise;
            this.set(key, data);
            this.loadingPromises.delete(key);
            return data;
        } catch (error) {
            this.loadingPromises.delete(key);
            throw error;
        }
    }

    async _loadBuses() {
        const response = await apiRequest('operator/buses');
        return response.buses || [];
    }

    /**
     * Charge les itinéraires avec cache
     */
    async getRoutes() {
        const key = 'routes';
        
        const cached = this.get(key);
        if (cached) return cached;

        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        const promise = this._loadRoutes();
        this.loadingPromises.set(key, promise);

        try {
            const data = await promise;
            this.set(key, data);
            this.loadingPromises.delete(key);
            return data;
        } catch (error) {
            this.loadingPromises.delete(key);
            throw error;
        }
    }

    async _loadRoutes() {
        const response = await apiRequest('operator/routes');
        return response.routes || [];
    }

    /**
     * Charge les commodités avec cache
     */
    async getAmenities() {
        const key = 'amenities';
        
        const cached = this.get(key);
        if (cached) return cached;

        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        const promise = this._loadAmenities();
        this.loadingPromises.set(key, promise);

        try {
            const data = await promise;
            this.set(key, data);
            this.loadingPromises.delete(key);
            return data;
        } catch (error) {
            this.loadingPromises.delete(key);
            throw error;
        }
    }

    async _loadAmenities() {
        const response = await apiRequest('operator/amenities');
        return response.amenities || [];
    }

    /**
     * Charge les plans de sièges avec cache
     */
    async getSeatPlans() {
        const key = 'seatPlans';
        
        const cached = this.get(key);
        if (cached) return cached;

        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        const promise = this._loadSeatPlans();
        this.loadingPromises.set(key, promise);

        try {
            const data = await promise;
            this.set(key, data);
            this.loadingPromises.delete(key);
            return data;
        } catch (error) {
            this.loadingPromises.delete(key);
            throw error;
        }
    }

    async _loadSeatPlans() {
        const response = await apiRequest('operator/seat-plans');
        return response.seat_plans || [];
    }

    /**
     * Charge les utilisateurs avec cache
     */
    async getUsers(role = null) {
        const key = role ? `users_${role}` : 'users';
        
        const cached = this.get(key);
        if (cached) return cached;

        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        const promise = this._loadUsers(role);
        this.loadingPromises.set(key, promise);

        try {
            const data = await promise;
            this.set(key, data);
            this.loadingPromises.delete(key);
            return data;
        } catch (error) {
            this.loadingPromises.delete(key);
            throw error;
        }
    }

    async _loadUsers(role = null) {
        const endpoint = role ? `operator/users?role=${role}` : 'operator/users';
        const response = await apiRequest(endpoint);
        return response.users || [];
    }

    /**
     * Invalide le cache pour une clé spécifique
     */
    invalidate(key) {
        this.delete(key);
    }

    /**
     * Invalide le cache pour toutes les données liées à un domaine
     */
    invalidateDomain(domain) {
        const keysToDelete = [];
        for (const key of this.cache.keys()) {
            if (key.startsWith(domain)) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => this.delete(key));
    }
}

// Instance globale du cache
window.dataCache = new DataCache();
